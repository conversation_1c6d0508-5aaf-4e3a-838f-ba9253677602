"""
Nutrition tracking router
"""
from typing import List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func

from app.database import get_db
from app.models import User, FoodItem, NutritionEntry
from app.schemas import (
    FoodItemCreate, FoodItem as FoodItemSchema,
    NutritionEntryCreate, NutritionEntry as NutritionEntrySchema,
    DailyNutritionSummary
)
from app.auth import get_current_active_user

router = APIRouter()

def calculate_nutrition_values(food_item: FoodItem, quantity: float, unit: str) -> dict:
    """Calculate nutritional values based on quantity and unit"""
    # Convert quantity to grams
    if unit == 'g':
        grams = quantity
    elif unit == 'serving':
        grams = quantity * food_item.serving_size_g
    elif unit == 'ml':
        grams = quantity  # Assume 1ml = 1g for liquids
    else:
        grams = quantity  # Default to grams
    
    # Calculate per 100g ratio
    ratio = grams / 100.0
    
    return {
        'calories': food_item.calories_per_100g * ratio,
        'protein': food_item.protein_per_100g * ratio,
        'carbs': food_item.carbs_per_100g * ratio,
        'fat': food_item.fat_per_100g * ratio,
        'fiber': food_item.fiber_per_100g * ratio,
        'sugar': food_item.sugar_per_100g * ratio,
        'sodium': food_item.sodium_per_100g * ratio
    }

# Food Items endpoints
@router.post("/foods", response_model=FoodItemSchema)
async def create_food_item(
    food_item: FoodItemCreate,
    db: Session = Depends(get_db)
):
    """Create a new food item"""
    db_food = FoodItem(**food_item.dict())
    db.add(db_food)
    db.commit()
    db.refresh(db_food)
    return db_food

@router.get("/foods", response_model=List[FoodItemSchema])
async def search_food_items(
    q: Optional[str] = Query(None, description="Search query"),
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """Search food items"""
    query = db.query(FoodItem)
    
    if q:
        search_term = f"%{q}%"
        query = query.filter(
            FoodItem.name.ilike(search_term) | 
            FoodItem.brand.ilike(search_term)
        )
    
    foods = query.offset(skip).limit(limit).all()
    return foods

@router.get("/foods/{food_id}", response_model=FoodItemSchema)
async def get_food_item(
    food_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific food item"""
    food = db.query(FoodItem).filter(FoodItem.id == food_id).first()
    if not food:
        raise HTTPException(status_code=404, detail="Food item not found")
    return food

# Nutrition Entries endpoints
@router.post("/entries", response_model=NutritionEntrySchema)
async def create_nutrition_entry(
    entry: NutritionEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new nutrition entry"""
    # Get food item to calculate nutrition values
    food_item = None
    if entry.food_item_id:
        food_item = db.query(FoodItem).filter(FoodItem.id == entry.food_item_id).first()
        if not food_item:
            raise HTTPException(status_code=404, detail="Food item not found")
    
    # Calculate nutritional values
    nutrition_values = {}
    if food_item:
        nutrition_values = calculate_nutrition_values(food_item, entry.quantity, entry.unit)
    
    db_entry = NutritionEntry(
        user_id=current_user.id,
        food_item_id=entry.food_item_id,
        meal_id=entry.meal_id,
        quantity=entry.quantity,
        unit=entry.unit,
        water_ml=entry.water_ml,
        caffeine_mg=entry.caffeine_mg,
        notes=entry.notes,
        **nutrition_values
    )
    
    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.get("/entries", response_model=List[NutritionEntrySchema])
async def get_nutrition_entries(
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get nutrition entries for the current user"""
    query = db.query(NutritionEntry).filter(NutritionEntry.user_id == current_user.id)
    
    if start_date:
        query = query.filter(func.date(NutritionEntry.consumed_at) >= start_date)
    if end_date:
        query = query.filter(func.date(NutritionEntry.consumed_at) <= end_date)
    
    entries = query.order_by(desc(NutritionEntry.consumed_at)).offset(skip).limit(limit).all()
    return entries

@router.get("/entries/daily-summary", response_model=DailyNutritionSummary)
async def get_daily_nutrition_summary(
    target_date: Optional[date] = Query(default_factory=date.today),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get daily nutrition summary for a specific date"""
    # Query entries for the target date
    entries = db.query(NutritionEntry).filter(
        and_(
            NutritionEntry.user_id == current_user.id,
            func.date(NutritionEntry.consumed_at) == target_date
        )
    ).all()
    
    # Calculate totals
    total_calories = sum(entry.calories or 0 for entry in entries)
    total_protein = sum(entry.protein or 0 for entry in entries)
    total_carbs = sum(entry.carbs or 0 for entry in entries)
    total_fat = sum(entry.fat or 0 for entry in entries)
    total_fiber = sum(entry.fiber or 0 for entry in entries)
    total_sodium = sum(entry.sodium or 0 for entry in entries)
    total_water_ml = sum(entry.water_ml or 0 for entry in entries)
    
    return DailyNutritionSummary(
        date=datetime.combine(target_date, datetime.min.time()),
        total_calories=total_calories,
        total_protein=total_protein,
        total_carbs=total_carbs,
        total_fat=total_fat,
        total_fiber=total_fiber,
        total_sodium=total_sodium,
        total_water_ml=total_water_ml
    )

@router.put("/entries/{entry_id}", response_model=NutritionEntrySchema)
async def update_nutrition_entry(
    entry_id: int,
    entry_update: NutritionEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a nutrition entry"""
    db_entry = db.query(NutritionEntry).filter(
        and_(
            NutritionEntry.id == entry_id,
            NutritionEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Nutrition entry not found")
    
    # Get food item for recalculation
    food_item = None
    if entry_update.food_item_id:
        food_item = db.query(FoodItem).filter(FoodItem.id == entry_update.food_item_id).first()
    
    # Update basic fields
    for field, value in entry_update.dict(exclude_unset=True).items():
        if field not in ['calories', 'protein', 'carbs', 'fat', 'fiber', 'sugar', 'sodium']:
            setattr(db_entry, field, value)
    
    # Recalculate nutrition values if food item and quantity changed
    if food_item and (entry_update.quantity or entry_update.unit):
        nutrition_values = calculate_nutrition_values(
            food_item, 
            entry_update.quantity or db_entry.quantity, 
            entry_update.unit or db_entry.unit
        )
        for key, value in nutrition_values.items():
            setattr(db_entry, key, value)
    
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.delete("/entries/{entry_id}")
async def delete_nutrition_entry(
    entry_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a nutrition entry"""
    db_entry = db.query(NutritionEntry).filter(
        and_(
            NutritionEntry.id == entry_id,
            NutritionEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Nutrition entry not found")
    
    db.delete(db_entry)
    db.commit()
    
    return {"message": "Nutrition entry deleted successfully"}
