"""
BMR Prediction Engine using machine learning
"""
import numpy as np
import pandas as pd
from typing import List, Optional, Tuple
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
from sqlalchemy.orm import Session

from app.models import User, BiometricEntry, NutritionEntry, ExerciseEntry
from app.utils import lbs_to_kg, inches_to_cm, calculate_bmr_us

class BMRPredictor:
    """
    Intelligent BMR prediction engine that learns from user data
    """
    
    def __init__(self):
        self.model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_names = [
            'weight_lbs', 'height_inches', 'age_days', 'gender_encoded',
            'body_fat_percentage', 'muscle_mass_lbs', 'water_percentage',
            'avg_daily_calories_7d', 'avg_daily_exercise_7d', 'weight_change_30d',
            'activity_level_encoded', 'days_since_start'
        ]
    
    def encode_gender(self, gender: str) -> int:
        """Encode gender as numeric value"""
        if not gender:
            return 0
        return 1 if gender.lower() == 'male' else 0
    
    def encode_activity_level(self, activity_level: str) -> int:
        """Encode activity level as numeric value"""
        levels = {
            'sedentary': 1,
            'lightly_active': 2,
            'moderately_active': 3,
            'very_active': 4,
            'extra_active': 5
        }
        return levels.get(activity_level, 2)  # Default to lightly_active
    
    def calculate_standard_bmr(self, user: User, weight_lbs: float, body_fat_percentage: Optional[float] = None) -> float:
        """
        Calculate standard BMR using established formulas with US units
        """
        if not user.date_of_birth or not user.height_inches:
            return 0

        age = (datetime.now().date() - user.date_of_birth.date()).days / 365.25

        return calculate_bmr_us(weight_lbs, user.height_inches, age, user.gender, body_fat_percentage)
    
    def extract_features(self, user: User, biometric_entry: BiometricEntry, db: Session) -> Optional[List[float]]:
        """
        Extract features for BMR prediction
        """
        if not user.date_of_birth or not user.height_inches or not biometric_entry.weight_lbs:
            return None

        # Basic user features
        age_days = (datetime.now().date() - user.date_of_birth.date()).days
        gender_encoded = self.encode_gender(user.gender)
        activity_level_encoded = self.encode_activity_level(user.activity_level)

        # Biometric features
        weight_lbs = biometric_entry.weight_lbs
        height_inches = user.height_inches
        body_fat_percentage = biometric_entry.body_fat_percentage or 0
        muscle_mass_lbs = biometric_entry.muscle_mass_lbs or 0
        water_percentage = biometric_entry.water_percentage or 0
        
        # Calculate derived features
        entry_date = biometric_entry.recorded_at.date()
        
        # Average daily calories (last 7 days)
        week_ago = entry_date - timedelta(days=7)
        nutrition_entries = db.query(NutritionEntry).filter(
            NutritionEntry.user_id == user.id,
            NutritionEntry.consumed_at >= week_ago,
            NutritionEntry.consumed_at <= entry_date
        ).all()
        
        total_calories = sum(entry.calories or 0 for entry in nutrition_entries)
        days_with_nutrition = len(set(entry.consumed_at.date() for entry in nutrition_entries))
        avg_daily_calories_7d = total_calories / max(days_with_nutrition, 1)
        
        # Average daily exercise (last 7 days)
        exercise_entries = db.query(ExerciseEntry).filter(
            ExerciseEntry.user_id == user.id,
            ExerciseEntry.performed_at >= week_ago,
            ExerciseEntry.performed_at <= entry_date
        ).all()
        
        total_exercise_calories = sum(entry.calories_burned or 0 for entry in exercise_entries)
        avg_daily_exercise_7d = total_exercise_calories / 7
        
        # Weight change over last 30 days
        month_ago = entry_date - timedelta(days=30)
        previous_entry = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == user.id,
            BiometricEntry.recorded_at >= month_ago,
            BiometricEntry.recorded_at < biometric_entry.recorded_at,
            BiometricEntry.weight_lbs.isnot(None)
        ).order_by(BiometricEntry.recorded_at.desc()).first()

        weight_change_30d = 0
        if previous_entry and previous_entry.weight_lbs:
            weight_change_30d = weight_lbs - previous_entry.weight_lbs
        
        # Days since user started tracking
        first_entry = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == user.id
        ).order_by(BiometricEntry.recorded_at).first()
        
        days_since_start = 0
        if first_entry:
            days_since_start = (entry_date - first_entry.recorded_at.date()).days
        
        return [
            weight_lbs, height_inches, age_days, gender_encoded,
            body_fat_percentage, muscle_mass_lbs, water_percentage,
            avg_daily_calories_7d, avg_daily_exercise_7d, weight_change_30d,
            activity_level_encoded, days_since_start
        ]
    
    def prepare_training_data(self, db: Session) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare training data from all users with sufficient data
        """
        features = []
        targets = []
        
        # Get all users with biometric data
        users_with_data = db.query(User).join(BiometricEntry).distinct().all()
        
        for user in users_with_data:
            # Get biometric entries for this user
            biometric_entries = db.query(BiometricEntry).filter(
                BiometricEntry.user_id == user.id,
                BiometricEntry.weight_lbs.isnot(None)
            ).order_by(BiometricEntry.recorded_at).all()

            for entry in biometric_entries:
                # Extract features
                feature_vector = self.extract_features(user, entry, db)
                if feature_vector is None:
                    continue

                # Calculate target BMR (use calculated BMR as ground truth for now)
                target_bmr = self.calculate_standard_bmr(user, entry.weight_lbs, entry.body_fat_percentage)
                if target_bmr <= 0:
                    continue

                features.append(feature_vector)
                targets.append(target_bmr)
        
        return np.array(features), np.array(targets)
    
    def train(self, db: Session) -> dict:
        """
        Train the BMR prediction model
        """
        X, y = self.prepare_training_data(db)
        
        if len(X) < 10:  # Need minimum data points
            return {
                "success": False,
                "message": "Insufficient training data",
                "data_points": len(X)
            }
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
        self.is_trained = True
        
        # Evaluate
        y_pred = self.model.predict(X_test_scaled)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        return {
            "success": True,
            "training_samples": len(X_train),
            "test_samples": len(X_test),
            "mae": mae,
            "r2_score": r2,
            "feature_importance": dict(zip(self.feature_names, self.model.feature_importances_))
        }
    
    def predict_bmr(self, user: User, biometric_entry: BiometricEntry, db: Session) -> Optional[float]:
        """
        Predict BMR for a specific user and biometric entry
        """
        if not self.is_trained:
            # Train the model if not already trained
            training_result = self.train(db)
            if not training_result["success"]:
                # Fall back to standard calculation
                return self.calculate_standard_bmr(user, biometric_entry.weight_kg, biometric_entry.body_fat_percentage)
        
        # Extract features
        features = self.extract_features(user, biometric_entry, db)
        if features is None:
            return self.calculate_standard_bmr(user, biometric_entry.weight_lbs, biometric_entry.body_fat_percentage)

        # Scale features and predict
        features_scaled = self.scaler.transform([features])
        predicted_bmr = self.model.predict(features_scaled)[0]

        # Sanity check - ensure prediction is reasonable
        standard_bmr = self.calculate_standard_bmr(user, biometric_entry.weight_lbs, biometric_entry.body_fat_percentage)
        
        # If prediction is too far from standard calculation, use weighted average
        if abs(predicted_bmr - standard_bmr) > standard_bmr * 0.3:  # 30% threshold
            predicted_bmr = (predicted_bmr + standard_bmr) / 2
        
        return max(predicted_bmr, 800)  # Minimum reasonable BMR
    
    def get_bmr_insights(self, user: User, db: Session) -> dict:
        """
        Get insights about BMR trends and factors
        """
        # Get recent biometric entries
        recent_entries = db.query(BiometricEntry).filter(
            BiometricEntry.user_id == user.id,
            BiometricEntry.weight_lbs.isnot(None)
        ).order_by(BiometricEntry.recorded_at.desc()).limit(10).all()
        
        if len(recent_entries) < 2:
            return {"message": "Insufficient data for insights"}
        
        # Calculate BMR trend
        bmr_values = []
        dates = []
        
        for entry in reversed(recent_entries):  # Reverse to get chronological order
            if self.is_trained:
                bmr = self.predict_bmr(user, entry, db)
            else:
                bmr = self.calculate_standard_bmr(user, entry.weight_lbs, entry.body_fat_percentage)
            
            if bmr:
                bmr_values.append(bmr)
                dates.append(entry.recorded_at.date())
        
        if len(bmr_values) < 2:
            return {"message": "Insufficient BMR data for trend analysis"}
        
        # Calculate trend
        bmr_change = bmr_values[-1] - bmr_values[0]
        days_span = (dates[-1] - dates[0]).days
        
        # Analyze factors
        weight_change = recent_entries[0].weight_lbs - recent_entries[-1].weight_lbs
        
        insights = {
            "current_bmr": bmr_values[-1],
            "bmr_change": bmr_change,
            "bmr_trend": "increasing" if bmr_change > 10 else "decreasing" if bmr_change < -10 else "stable",
            "weight_change_lbs": weight_change,
            "analysis_period_days": days_span,
            "recommendations": []
        }
        
        # Generate recommendations
        if bmr_change < -50:
            insights["recommendations"].append("Your BMR appears to be decreasing. Consider increasing protein intake and resistance training.")
        elif bmr_change > 50:
            insights["recommendations"].append("Your BMR is increasing, which is great for metabolism!")
        
        if weight_change > 2:
            insights["recommendations"].append("Weight loss detected. Monitor BMR to ensure it's not dropping too quickly.")
        
        return insights

# Global instance
bmr_predictor = BMRPredictor()
