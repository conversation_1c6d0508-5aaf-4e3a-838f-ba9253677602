"""
Migration script to convert existing data from metric to US units
"""
import sqlite3
import os
from datetime import datetime, timedelta
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def migrate_database():
    """Migrate existing database to US units or create new one"""
    db_path = 'weightloss_tracker.db'
    
    # Backup existing database if it exists
    if os.path.exists(db_path):
        backup_path = f'weightloss_tracker_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        os.rename(db_path, backup_path)
        print(f"Existing database backed up to: {backup_path}")
    
    # Create new database with US units
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create tables with US units
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            username TEXT UNIQUE NOT NULL,
            hashed_password TEXT NOT NULL,
            first_name TEXT,
            last_name TEXT,
            date_of_birth TEXT,
            gender TEXT,
            height_inches REAL,
            activity_level TEXT,
            goal_weight_lbs REAL,
            goal_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS biometric_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            recorded_at TEXT DEFAULT CURRENT_TIMESTAMP,
            weight_lbs REAL,
            body_fat_percentage REAL,
            water_percentage REAL,
            bone_mass_lbs REAL,
            muscle_mass_lbs REAL,
            visceral_fat_rating REAL,
            bmi REAL,
            bmr_calculated REAL,
            bmr_predicted REAL,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS food_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            brand TEXT,
            barcode TEXT,
            calories_per_100g REAL NOT NULL,
            protein_per_100g REAL DEFAULT 0,
            carbs_per_100g REAL DEFAULT 0,
            fat_per_100g REAL DEFAULT 0,
            fiber_per_100g REAL DEFAULT 0,
            sugar_per_100g REAL DEFAULT 0,
            sodium_per_100g REAL DEFAULT 0,
            serving_size_g REAL DEFAULT 100,
            serving_description TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            is_verified BOOLEAN DEFAULT 0
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS nutrition_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            food_item_id INTEGER,
            meal_id INTEGER,
            consumed_at TEXT DEFAULT CURRENT_TIMESTAMP,
            quantity REAL NOT NULL,
            unit TEXT DEFAULT 'g',
            calories REAL,
            protein REAL,
            carbs REAL,
            fat REAL,
            fiber REAL,
            sugar REAL,
            sodium REAL,
            water_ml REAL DEFAULT 0,
            caffeine_mg REAL DEFAULT 0,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (food_item_id) REFERENCES food_items (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS meals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            meal_type TEXT,
            is_template BOOLEAN DEFAULT 0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS meal_ingredients (
            meal_id INTEGER,
            food_item_id INTEGER,
            quantity REAL DEFAULT 1.0,
            unit TEXT DEFAULT 'serving',
            FOREIGN KEY (meal_id) REFERENCES meals (id),
            FOREIGN KEY (food_item_id) REFERENCES food_items (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS exercise_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            category TEXT,
            met_value REAL,
            description TEXT
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS exercise_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            exercise_type_id INTEGER,
            performed_at TEXT DEFAULT CURRENT_TIMESTAMP,
            duration_minutes REAL,
            intensity TEXT,
            calories_burned REAL,
            steps INTEGER,
            distance_miles REAL,
            heart_rate_avg INTEGER,
            heart_rate_max INTEGER,
            sets INTEGER,
            reps INTEGER,
            weight_lbs REAL,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (exercise_type_id) REFERENCES exercise_types (id)
        )
    ''')
    
    # Create sample user with US units
    password_hash = pwd_context.hash("demo123")
    cursor.execute('''
        INSERT OR REPLACE INTO users 
        (id, email, username, hashed_password, first_name, last_name, date_of_birth, 
         gender, height_inches, activity_level, goal_weight_lbs, goal_type)
        VALUES (1, '<EMAIL>', 'demo', ?, 'Demo', 'User', '1990-01-01', 
                'male', 69, 'moderately_active', 165, 'lose_weight')
    ''', (password_hash,))
    
    # Create sample food items
    foods = [
        ('Chicken Breast', 'Generic', None, 165, 31, 0, 3.6, 0, 0, 74, 100, '100g'),
        ('Brown Rice', 'Generic', None, 111, 2.6, 23, 0.9, 1.8, 0.4, 5, 100, '100g cooked'),
        ('Broccoli', 'Generic', None, 34, 2.8, 7, 0.4, 2.6, 1.5, 33, 100, '100g'),
        ('Banana', 'Generic', None, 89, 1.1, 23, 0.3, 2.6, 12, 1, 100, '1 medium'),
        ('Oatmeal', 'Generic', None, 68, 2.4, 12, 1.4, 1.7, 0.3, 49, 100, '100g cooked'),
        ('Salmon', 'Generic', None, 208, 20, 0, 12, 0, 0, 59, 100, '100g'),
        ('Greek Yogurt', 'Generic', None, 59, 10, 3.6, 0.4, 0, 3.6, 36, 100, '100g'),
        ('Almonds', 'Generic', None, 579, 21, 22, 50, 12, 4.4, 1, 28, '28g (1 oz)'),
    ]
    
    for food in foods:
        cursor.execute('''
            INSERT OR REPLACE INTO food_items 
            (name, brand, barcode, calories_per_100g, protein_per_100g, carbs_per_100g, 
             fat_per_100g, fiber_per_100g, sugar_per_100g, sodium_per_100g, 
             serving_size_g, serving_description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', food)
    
    # Create sample biometric entries (last 30 days) with US units
    base_weight = 176.0  # lbs
    for i in range(30):
        date = datetime.now() - timedelta(days=29-i)
        # Simulate gradual weight loss
        weight = base_weight - (i * 0.2) + (i % 3 * 0.1)  # Some variation
        body_fat = 18.0 - (i * 0.05)
        water = 60.0 + (i % 5 * 0.2)
        muscle = 77.0 + (i * 0.04)  # lbs
        bone_mass = 7.5 + (i % 4 * 0.1)  # lbs
        bmi = (weight / (69 ** 2)) * 703  # US BMI calculation
        # Convert to kg for BMR calculation
        weight_kg = weight * 0.453592
        height_cm = 69 * 2.54
        bmr = 10 * weight_kg + 6.25 * height_cm - 5 * 33 + 5  # Mifflin-St Jeor for male
        
        cursor.execute('''
            INSERT INTO biometric_entries 
            (user_id, recorded_at, weight_lbs, body_fat_percentage, water_percentage, 
             bone_mass_lbs, muscle_mass_lbs, visceral_fat_rating, bmi, bmr_calculated)
            VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (date.isoformat(), weight, body_fat, water, bone_mass, muscle, 8.0, bmi, bmr))
    
    # Create sample nutrition entries
    for i in range(15):  # Last 15 days
        date = datetime.now() - timedelta(days=14-i)
        
        # Breakfast
        cursor.execute('''
            INSERT INTO nutrition_entries 
            (user_id, food_item_id, consumed_at, quantity, unit, calories, protein, carbs, fat)
            VALUES (1, 5, ?, 100, 'g', 68, 2.4, 12, 1.4)
        ''', ((date + timedelta(hours=8)).isoformat(),))
        
        # Lunch
        cursor.execute('''
            INSERT INTO nutrition_entries 
            (user_id, food_item_id, consumed_at, quantity, unit, calories, protein, carbs, fat)
            VALUES (1, 1, ?, 150, 'g', 248, 46.5, 0, 5.4)
        ''', ((date + timedelta(hours=12)).isoformat(),))
        
        # Dinner
        cursor.execute('''
            INSERT INTO nutrition_entries 
            (user_id, food_item_id, consumed_at, quantity, unit, calories, protein, carbs, fat)
            VALUES (1, 6, ?, 120, 'g', 250, 24, 0, 14.4)
        ''', ((date + timedelta(hours=19)).isoformat(),))
    
    # Add some exercise types
    exercise_types = [
        ('Running', 'cardio', 8.0, 'Running at moderate pace'),
        ('Walking', 'cardio', 3.5, 'Walking at normal pace'),
        ('Weight Training', 'strength', 6.0, 'General weight lifting'),
        ('Cycling', 'cardio', 7.5, 'Cycling at moderate pace'),
        ('Swimming', 'cardio', 8.0, 'Swimming laps'),
    ]
    
    for exercise in exercise_types:
        cursor.execute('''
            INSERT OR REPLACE INTO exercise_types (name, category, met_value, description)
            VALUES (?, ?, ?, ?)
        ''', exercise)
    
    conn.commit()
    conn.close()
    print("Database migrated to US units successfully!")
    print("Demo account: username='demo', password='demo123'")

if __name__ == "__main__":
    migrate_database()
