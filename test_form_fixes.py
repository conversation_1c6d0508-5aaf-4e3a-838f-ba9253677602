"""
Test that form button issues are fixed
"""

def test_form_structure():
    """Test that forms are properly structured"""
    print("Testing form structure fixes...")
    
    # Read the streamlit app file
    with open('streamlit_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for problematic patterns
    issues = []
    
    # Check for st.button inside forms (should be st.form_submit_button)
    lines = content.split('\n')
    in_form = False
    form_depth = 0
    
    for i, line in enumerate(lines, 1):
        # Track form context
        if 'with st.form(' in line:
            in_form = True
            form_depth += 1
        elif in_form and 'with st.' in line and 'form' not in line:
            # Other with statements might end the form context
            pass
        elif in_form and line.strip().startswith('if st.button('):
            issues.append(f"Line {i}: st.button() found inside form - should use st.form_submit_button()")
        elif 'st.form_submit_button(' in line:
            # This is correct
            pass
    
    # Check for nested buttons
    button_count = content.count('if st.button(')
    form_submit_count = content.count('st.form_submit_button(')
    
    print(f"Found {button_count} regular buttons")
    print(f"Found {form_submit_count} form submit buttons")
    
    if issues:
        print("❌ Found form structure issues:")
        for issue in issues:
            print(f"   {issue}")
    else:
        print("✅ No form structure issues found!")
    
    # Check for specific fixes
    fixes_verified = []
    
    # Check sync fitness form
    if 'with st.form("sync_fitness_form"):' in content:
        fixes_verified.append("✅ Sync fitness data moved to form")
    
    # Check meal creation restructure
    if 'st.form("create_meal_form"):' in content and 'Add Ingredients' in content:
        fixes_verified.append("✅ Meal creation form restructured")
    
    # Check meal logging form
    if 'st.form(f"log_meal_form_{meal_data[\'id\']}"):' in content:
        fixes_verified.append("✅ Meal logging form properly structured")
    
    print("\nVerified fixes:")
    for fix in fixes_verified:
        print(f"   {fix}")
    
    return len(issues) == 0

if __name__ == "__main__":
    success = test_form_structure()
    
    print(f"\n🎉 Form structure test {'PASSED' if success else 'FAILED'}!")
    print("\nFixed Issues:")
    print("✅ Moved sync fitness data to proper form")
    print("✅ Restructured meal creation to separate ingredients from form")
    print("✅ Fixed nested button issues in meal logging")
    print("✅ All buttons now properly use form_submit_button inside forms")
