"""
Test registration with US units
"""
import requests
import json

API_BASE_URL = "http://localhost:8000/api"

def test_registration():
    """Test user registration with US units"""
    
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "testpass123",
        "first_name": "Test",
        "last_name": "User",
        "date_of_birth": "1995-06-15",  # Date only format
        "gender": "male",
        "height_inches": 72,  # 6 feet
        "activity_level": "moderately_active",
        "goal_weight_lbs": 180,
        "goal_type": "lose_weight"
    }
    
    print("Testing registration with US units...")
    response = requests.post(f"{API_BASE_URL}/auth/register", json=user_data)
    
    if response.status_code == 200:
        user = response.json()
        print(f"✅ Registration successful!")
        print(f"   User: {user['username']}")
        print(f"   Height: {user['height_inches']}\" (6 feet)")
        print(f"   Goal Weight: {user['goal_weight_lbs']} lbs")
        
        # Test login with new user
        print("\nTesting login with new user...")
        login_response = requests.post(
            f"{API_BASE_URL}/auth/login",
            data={"username": "testuser", "password": "testpass123"}
        )
        
        if login_response.status_code == 200:
            print("✅ Login successful with new user!")
        else:
            print(f"❌ Login failed: {login_response.text}")
            
    else:
        print(f"❌ Registration failed: {response.text}")

if __name__ == "__main__":
    test_registration()
