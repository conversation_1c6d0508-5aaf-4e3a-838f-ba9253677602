"""
Startup script for the Weight Loss Tracker application
"""
import subprocess
import sys
import time
import threading
import os

def install_requirements():
    """Install required packages"""
    packages = [
        'fastapi==0.104.1',
        'uvicorn[standard]==0.24.0',
        'streamlit==1.28.1',
        'sqlalchemy==2.0.23',
        'pandas==2.1.3',
        'numpy==1.25.2',
        'scikit-learn==1.3.2',
        'plotly==5.17.0',
        'python-jose[cryptography]==3.3.0',
        'passlib[bcrypt]==1.7.4',
        'python-multipart==0.0.6',
        'requests==2.31.0',
        'pydantic==2.5.0',
        'python-dateutil==2.8.2'
    ]
    
    print("Installing required packages...")
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✓ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"✗ Failed to install {package}")
    
    print("Package installation completed!")

def setup_database():
    """Setup database with sample data"""
    print("Setting up database with sample data...")
    try:
        exec(open('setup_sample_data.py').read())
        print("✓ Database setup completed!")
    except Exception as e:
        print(f"✗ Database setup failed: {e}")

def start_api_server():
    """Start the FastAPI server"""
    print("Starting API server...")
    try:
        subprocess.run([
            sys.executable, '-m', 'uvicorn', 
            'app.main:app', 
            '--host', '0.0.0.0', 
            '--port', '8000', 
            '--reload'
        ])
    except KeyboardInterrupt:
        print("API server stopped.")

def start_streamlit_app():
    """Start the Streamlit app"""
    print("Starting Streamlit app...")
    time.sleep(5)  # Wait for API server to start
    try:
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 
            'streamlit_app.py', 
            '--server.port', '8501'
        ])
    except KeyboardInterrupt:
        print("Streamlit app stopped.")

def main():
    """Main startup function"""
    print("🏃‍♂️ Weight Loss Tracker - Starting Application...")
    print("=" * 50)
    
    # Install requirements
    install_requirements()
    
    # Setup database
    setup_database()
    
    print("\n" + "=" * 50)
    print("Starting servers...")
    print("API Server will be available at: http://localhost:8000")
    print("Streamlit App will be available at: http://localhost:8501")
    print("=" * 50)
    
    # Start API server in a separate thread
    api_thread = threading.Thread(target=start_api_server, daemon=True)
    api_thread.start()
    
    # Start Streamlit app
    start_streamlit_app()

if __name__ == "__main__":
    main()
