"""
Biometrics tracking router
"""
from typing import List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.database import get_db
from app.models import User, BiometricEntry
from app.schemas import BiometricEntryCreate, BiometricEntry as BiometricEntrySchema
from app.auth import get_current_active_user
from app.utils import calculate_bmi_us, calculate_bmr_us, inches_to_cm

router = APIRouter()

def calculate_bmr_for_user(user: User, weight_lbs: float, body_fat_percentage: Optional[float] = None) -> float:
    """
    Calculate BMR using US units and multiple methods:
    1. Mifflin-St Jeor equation (standard)
    2. Katch-Mc<PERSON>rdle equation (if body fat % available)
    """
    if not user.date_of_birth or not user.height_inches:
        return 0

    age = (datetime.now().date() - user.date_of_birth.date()).days / 365.25

    return calculate_bmr_us(weight_lbs, user.height_inches, age, user.gender, body_fat_percentage)

@router.post("/", response_model=BiometricEntrySchema)
async def create_biometric_entry(
    entry: BiometricEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new biometric entry"""
    # Calculate BMI if weight is provided
    bmi = None
    if entry.weight_lbs and current_user.height_inches:
        bmi = calculate_bmi_us(entry.weight_lbs, current_user.height_inches)

    # Calculate BMR if weight is provided
    bmr_calculated = None
    if entry.weight_lbs:
        bmr_calculated = calculate_bmr_for_user(current_user, entry.weight_lbs, entry.body_fat_percentage)

    db_entry = BiometricEntry(
        user_id=current_user.id,
        weight_lbs=entry.weight_lbs,
        body_fat_percentage=entry.body_fat_percentage,
        water_percentage=entry.water_percentage,
        bone_mass_lbs=entry.bone_mass_lbs,
        muscle_mass_lbs=entry.muscle_mass_lbs,
        visceral_fat_rating=entry.visceral_fat_rating,
        bmi=bmi or entry.bmi,
        bmr_calculated=bmr_calculated,
        notes=entry.notes
    )
    
    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.get("/", response_model=List[BiometricEntrySchema])
async def get_biometric_entries(
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get biometric entries for the current user"""
    query = db.query(BiometricEntry).filter(BiometricEntry.user_id == current_user.id)
    
    if start_date:
        query = query.filter(BiometricEntry.recorded_at >= start_date)
    if end_date:
        query = query.filter(BiometricEntry.recorded_at <= end_date)
    
    entries = query.order_by(desc(BiometricEntry.recorded_at)).offset(skip).limit(limit).all()
    return entries

@router.get("/latest", response_model=Optional[BiometricEntrySchema])
async def get_latest_biometric_entry(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get the most recent biometric entry"""
    entry = db.query(BiometricEntry).filter(
        BiometricEntry.user_id == current_user.id
    ).order_by(desc(BiometricEntry.recorded_at)).first()
    
    return entry

@router.get("/{entry_id}", response_model=BiometricEntrySchema)
async def get_biometric_entry(
    entry_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific biometric entry"""
    entry = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.id == entry_id,
            BiometricEntry.user_id == current_user.id
        )
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Biometric entry not found")
    
    return entry

@router.put("/{entry_id}", response_model=BiometricEntrySchema)
async def update_biometric_entry(
    entry_id: int,
    entry_update: BiometricEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a biometric entry"""
    db_entry = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.id == entry_id,
            BiometricEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Biometric entry not found")
    
    # Update fields
    for field, value in entry_update.dict(exclude_unset=True).items():
        setattr(db_entry, field, value)
    
    # Recalculate BMI and BMR if weight changed
    if entry_update.weight_lbs and current_user.height_inches:
        db_entry.bmi = calculate_bmi_us(entry_update.weight_lbs, current_user.height_inches)
        db_entry.bmr_calculated = calculate_bmr_for_user(current_user, entry_update.weight_lbs, entry_update.body_fat_percentage)
    
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.delete("/{entry_id}")
async def delete_biometric_entry(
    entry_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a biometric entry"""
    db_entry = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.id == entry_id,
            BiometricEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Biometric entry not found")
    
    db.delete(db_entry)
    db.commit()
    
    return {"message": "Biometric entry deleted successfully"}
