"""
Biometrics tracking router
"""
from typing import List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.database import get_db
from app.models import User, BiometricEntry
from app.schemas import BiometricEntryCreate, BiometricEntry as BiometricEntrySchema
from app.auth import get_current_active_user

router = APIRouter()

def calculate_bmi(weight_kg: float, height_cm: float) -> float:
    """Calculate BMI from weight and height"""
    if height_cm <= 0:
        return 0
    height_m = height_cm / 100
    return weight_kg / (height_m ** 2)

def calculate_bmr(user: User, weight_kg: float, body_fat_percentage: Optional[float] = None) -> float:
    """
    Calculate BMR using multiple methods:
    1. Mifflin-<PERSON> Je<PERSON> equation (standard)
    2. Katch-Mc<PERSON>rdle equation (if body fat % available)
    """
    if not user.date_of_birth or not user.height_cm:
        return 0
    
    age = (datetime.now().date() - user.date_of_birth.date()).days / 365.25
    height_cm = user.height_cm
    
    # Mifflin-St Jeor equation
    if user.gender and user.gender.lower() == 'male':
        bmr_mifflin = 10 * weight_kg + 6.25 * height_cm - 5 * age + 5
    else:
        bmr_mifflin = 10 * weight_kg + 6.25 * height_cm - 5 * age - 161
    
    # If body fat percentage is available, use Katch-McArdle
    if body_fat_percentage and body_fat_percentage > 0:
        lean_body_mass = weight_kg * (1 - body_fat_percentage / 100)
        bmr_katch = 370 + (21.6 * lean_body_mass)
        # Average the two methods
        return (bmr_mifflin + bmr_katch) / 2
    
    return bmr_mifflin

@router.post("/", response_model=BiometricEntrySchema)
async def create_biometric_entry(
    entry: BiometricEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new biometric entry"""
    # Calculate BMI if weight is provided
    bmi = None
    if entry.weight_kg and current_user.height_cm:
        bmi = calculate_bmi(entry.weight_kg, current_user.height_cm)
    
    # Calculate BMR if weight is provided
    bmr_calculated = None
    if entry.weight_kg:
        bmr_calculated = calculate_bmr(current_user, entry.weight_kg, entry.body_fat_percentage)
    
    db_entry = BiometricEntry(
        user_id=current_user.id,
        weight_kg=entry.weight_kg,
        body_fat_percentage=entry.body_fat_percentage,
        water_percentage=entry.water_percentage,
        bone_mass_kg=entry.bone_mass_kg,
        muscle_mass_kg=entry.muscle_mass_kg,
        visceral_fat_rating=entry.visceral_fat_rating,
        bmi=bmi or entry.bmi,
        bmr_calculated=bmr_calculated,
        notes=entry.notes
    )
    
    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.get("/", response_model=List[BiometricEntrySchema])
async def get_biometric_entries(
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get biometric entries for the current user"""
    query = db.query(BiometricEntry).filter(BiometricEntry.user_id == current_user.id)
    
    if start_date:
        query = query.filter(BiometricEntry.recorded_at >= start_date)
    if end_date:
        query = query.filter(BiometricEntry.recorded_at <= end_date)
    
    entries = query.order_by(desc(BiometricEntry.recorded_at)).offset(skip).limit(limit).all()
    return entries

@router.get("/latest", response_model=Optional[BiometricEntrySchema])
async def get_latest_biometric_entry(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get the most recent biometric entry"""
    entry = db.query(BiometricEntry).filter(
        BiometricEntry.user_id == current_user.id
    ).order_by(desc(BiometricEntry.recorded_at)).first()
    
    return entry

@router.get("/{entry_id}", response_model=BiometricEntrySchema)
async def get_biometric_entry(
    entry_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific biometric entry"""
    entry = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.id == entry_id,
            BiometricEntry.user_id == current_user.id
        )
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Biometric entry not found")
    
    return entry

@router.put("/{entry_id}", response_model=BiometricEntrySchema)
async def update_biometric_entry(
    entry_id: int,
    entry_update: BiometricEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a biometric entry"""
    db_entry = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.id == entry_id,
            BiometricEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Biometric entry not found")
    
    # Update fields
    for field, value in entry_update.dict(exclude_unset=True).items():
        setattr(db_entry, field, value)
    
    # Recalculate BMI and BMR if weight changed
    if entry_update.weight_kg and current_user.height_cm:
        db_entry.bmi = calculate_bmi(entry_update.weight_kg, current_user.height_cm)
        db_entry.bmr_calculated = calculate_bmr(current_user, entry_update.weight_kg, entry_update.body_fat_percentage)
    
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.delete("/{entry_id}")
async def delete_biometric_entry(
    entry_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a biometric entry"""
    db_entry = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.id == entry_id,
            BiometricEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Biometric entry not found")
    
    db.delete(db_entry)
    db.commit()
    
    return {"message": "Biometric entry deleted successfully"}
