"""
Configuration settings for the Weight Loss Tracker application
"""
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database
    database_url: str = "sqlite:///./weightloss_tracker.db"
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # API Keys (for future integrations)
    nutrition_api_key: Optional[str] = None
    fitness_tracker_api_key: Optional[str] = None
    
    # Application
    app_name: str = "Weight Loss Tracker"
    debug: bool = True
    
    class Config:
        env_file = ".env"

settings = Settings()
