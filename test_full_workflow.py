"""
Test complete workflow with US units
"""
import requests
import json

API_BASE_URL = "http://localhost:8000/api"

def test_complete_workflow():
    """Test complete user workflow with US units"""
    
    # Login with test user
    print("Logging in with test user...")
    login_response = requests.post(
        f"{API_BASE_URL}/auth/login",
        data={"username": "testuser", "password": "testpass123"}
    )
    
    if login_response.status_code != 200:
        print("❌ Login failed, cannot continue test")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful!")
    
    # Add biometric entry
    print("\nAdding biometric entry...")
    biometric_data = {
        "weight_lbs": 185.5,
        "body_fat_percentage": 16.5,
        "water_percentage": 62.0,
        "muscle_mass_lbs": 82.3,
        "bone_mass_lbs": 8.2,
        "visceral_fat_rating": 7.0,
        "notes": "Morning weigh-in after workout"
    }
    
    bio_response = requests.post(
        f"{API_BASE_URL}/biometrics/",
        headers=headers,
        json=biometric_data
    )
    
    if bio_response.status_code == 200:
        bio_entry = bio_response.json()
        print(f"✅ Biometric entry created!")
        print(f"   Weight: {bio_entry['weight_lbs']} lbs")
        print(f"   BMI: {bio_entry['bmi']:.1f}")
        print(f"   BMR: {bio_entry['bmr_calculated']:.0f} calories/day")
    else:
        print(f"❌ Biometric entry failed: {bio_response.text}")
    
    # Add nutrition entry
    print("\nAdding nutrition entry...")
    nutrition_data = {
        "food_item_id": 1,  # Chicken breast
        "quantity": 200,  # 200g serving
        "unit": "g",
        "notes": "Grilled chicken breast for lunch"
    }
    
    nutrition_response = requests.post(
        f"{API_BASE_URL}/nutrition/entries",
        headers=headers,
        json=nutrition_data
    )
    
    if nutrition_response.status_code == 200:
        nutrition_entry = nutrition_response.json()
        print(f"✅ Nutrition entry created!")
        print(f"   Food: Chicken breast (200g)")
        print(f"   Calories: {nutrition_entry['calories']:.0f}")
        print(f"   Protein: {nutrition_entry['protein']:.1f}g")
    else:
        print(f"❌ Nutrition entry failed: {nutrition_response.text}")
    
    # Get analytics
    print("\nGetting analytics...")
    analytics_response = requests.get(
        f"{API_BASE_URL}/analytics/progress-summary",
        headers=headers
    )
    
    if analytics_response.status_code == 200:
        analytics = analytics_response.json()
        print(f"✅ Analytics retrieved!")
        print(f"   Current Weight: {analytics.get('current_weight_lbs', 'N/A')} lbs")
        print(f"   Goal Weight: {analytics.get('goal_weight_lbs', 'N/A')} lbs")
        print(f"   Current BMI: {analytics.get('current_bmi', 'N/A')}")
    else:
        print(f"❌ Analytics failed: {analytics_response.text}")
    
    print("\n🎉 Complete workflow test successful with US units!")

if __name__ == "__main__":
    test_complete_workflow()
