"""
Utility functions for unit conversions and calculations
"""

def lbs_to_kg(lbs: float) -> float:
    """Convert pounds to kilograms"""
    return lbs * 0.453592

def kg_to_lbs(kg: float) -> float:
    """Convert kilograms to pounds"""
    return kg * 2.20462

def inches_to_cm(inches: float) -> float:
    """Convert inches to centimeters"""
    return inches * 2.54

def cm_to_inches(cm: float) -> float:
    """Convert centimeters to inches"""
    return cm / 2.54

def miles_to_km(miles: float) -> float:
    """Convert miles to kilometers"""
    return miles * 1.60934

def km_to_miles(km: float) -> float:
    """Convert kilometers to miles"""
    return km / 1.60934

def calculate_bmi_us(weight_lbs: float, height_inches: float) -> float:
    """Calculate BMI using US units (lbs and inches)"""
    if height_inches <= 0:
        return 0
    return (weight_lbs / (height_inches ** 2)) * 703

def calculate_bmr_us(weight_lbs: float, height_inches: float, age_years: float, gender: str, body_fat_percentage: float = None) -> float:
    """
    Calculate BMR using US units
    Uses Mifflin-St Jeor equation and optionally Katch-McArdle if body fat % is available
    """
    # Convert to metric for calculation
    weight_kg = lbs_to_kg(weight_lbs)
    height_cm = inches_to_cm(height_inches)
    
    # Mifflin-St Jeor equation
    if gender and gender.lower() == 'male':
        bmr_mifflin = 10 * weight_kg + 6.25 * height_cm - 5 * age_years + 5
    else:
        bmr_mifflin = 10 * weight_kg + 6.25 * height_cm - 5 * age_years - 161
    
    # If body fat percentage is available, use Katch-McArdle
    if body_fat_percentage and body_fat_percentage > 0:
        lean_body_mass_kg = weight_kg * (1 - body_fat_percentage / 100)
        bmr_katch = 370 + (21.6 * lean_body_mass_kg)
        # Average the two methods
        return (bmr_mifflin + bmr_katch) / 2
    
    return bmr_mifflin

def format_weight(weight_lbs: float) -> str:
    """Format weight for display"""
    return f"{weight_lbs:.1f} lbs"

def format_height(height_inches: float) -> str:
    """Format height for display (feet and inches)"""
    feet = int(height_inches // 12)
    inches = height_inches % 12
    return f"{feet}'{inches:.1f}\""

def format_distance(distance_miles: float) -> str:
    """Format distance for display"""
    return f"{distance_miles:.2f} miles"

def height_feet_inches_to_inches(feet: int, inches: float) -> float:
    """Convert feet and inches to total inches"""
    return feet * 12 + inches

def inches_to_feet_inches(total_inches: float) -> tuple:
    """Convert total inches to feet and inches"""
    feet = int(total_inches // 12)
    inches = total_inches % 12
    return feet, inches
