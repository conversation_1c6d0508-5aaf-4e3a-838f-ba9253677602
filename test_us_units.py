"""
Test script to verify US units are working correctly
"""
import requests
import json

API_BASE_URL = "http://localhost:8000/api"

def test_us_units():
    """Test that the API is working with US units"""
    
    # Test login with demo account
    print("Testing login...")
    login_response = requests.post(
        f"{API_BASE_URL}/auth/login",
        data={"username": "demo", "password": "demo123"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.text}")
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    print("✅ Login successful!")
    
    # Test getting user profile
    print("\nTesting user profile...")
    profile_response = requests.get(f"{API_BASE_URL}/auth/me", headers=headers)
    if profile_response.status_code == 200:
        profile = profile_response.json()
        print(f"✅ User profile: height={profile.get('height_inches')}\" goal_weight={profile.get('goal_weight_lbs')} lbs")
    else:
        print(f"❌ Profile failed: {profile_response.text}")
    
    # Test getting biometric data
    print("\nTesting biometric data...")
    bio_response = requests.get(f"{API_BASE_URL}/biometrics/", headers=headers)
    if bio_response.status_code == 200:
        bio_data = bio_response.json()
        if bio_data:
            latest = bio_data[0]
            print(f"✅ Latest biometric: weight={latest.get('weight_lbs')} lbs, muscle_mass={latest.get('muscle_mass_lbs')} lbs")
        else:
            print("⚠️ No biometric data found")
    else:
        print(f"❌ Biometric data failed: {bio_response.text}")
    
    # Test weight trend
    print("\nTesting weight trend...")
    trend_response = requests.get(f"{API_BASE_URL}/analytics/weight-trend", headers=headers)
    if trend_response.status_code == 200:
        trend_data = trend_response.json()
        if trend_data:
            print(f"✅ Weight trend: {len(trend_data)} entries, latest weight={trend_data[-1].get('weight_lbs')} lbs")
        else:
            print("⚠️ No weight trend data")
    else:
        print(f"❌ Weight trend failed: {trend_response.text}")
    
    # Test creating a new biometric entry
    print("\nTesting new biometric entry...")
    new_entry = {
        "weight_lbs": 175.5,
        "body_fat_percentage": 15.0,
        "muscle_mass_lbs": 78.0,
        "notes": "Test entry with US units"
    }
    
    create_response = requests.post(
        f"{API_BASE_URL}/biometrics/",
        headers=headers,
        json=new_entry
    )
    
    if create_response.status_code == 200:
        created = create_response.json()
        print(f"✅ Created entry: weight={created.get('weight_lbs')} lbs, BMI={created.get('bmi'):.1f}")
    else:
        print(f"❌ Create entry failed: {create_response.text}")
    
    print("\n🎉 US units conversion test completed!")

if __name__ == "__main__":
    test_us_units()
