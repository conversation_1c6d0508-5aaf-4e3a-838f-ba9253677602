"""
Setup script to create sample data for the Weight Loss Tracker
"""
import sqlite3
from datetime import datetime, timedelta
import hashlib

def create_sample_data():
    """Create sample data for testing"""
    # Connect to database
    conn = sqlite3.connect('weightloss_tracker.db')
    cursor = conn.cursor()
    
    # Create tables (simplified version)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            username TEXT UNIQUE NOT NULL,
            hashed_password TEXT NOT NULL,
            first_name TEXT,
            last_name TEXT,
            date_of_birth TEXT,
            gender TEXT,
            height_cm REAL,
            activity_level TEXT,
            goal_weight_kg REAL,
            goal_type TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS biometric_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            recorded_at TEXT DEFAULT CURRENT_TIMESTAMP,
            weight_kg REAL,
            body_fat_percentage REAL,
            water_percentage REAL,
            bone_mass_kg REAL,
            muscle_mass_kg REAL,
            visceral_fat_rating REAL,
            bmi REAL,
            bmr_calculated REAL,
            bmr_predicted REAL,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS food_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            brand TEXT,
            barcode TEXT,
            calories_per_100g REAL NOT NULL,
            protein_per_100g REAL DEFAULT 0,
            carbs_per_100g REAL DEFAULT 0,
            fat_per_100g REAL DEFAULT 0,
            fiber_per_100g REAL DEFAULT 0,
            sugar_per_100g REAL DEFAULT 0,
            sodium_per_100g REAL DEFAULT 0,
            serving_size_g REAL DEFAULT 100,
            serving_description TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            is_verified BOOLEAN DEFAULT 0
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS nutrition_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            food_item_id INTEGER,
            meal_id INTEGER,
            consumed_at TEXT DEFAULT CURRENT_TIMESTAMP,
            quantity REAL NOT NULL,
            unit TEXT DEFAULT 'g',
            calories REAL,
            protein REAL,
            carbs REAL,
            fat REAL,
            fiber REAL,
            sugar REAL,
            sodium REAL,
            water_ml REAL DEFAULT 0,
            caffeine_mg REAL DEFAULT 0,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (food_item_id) REFERENCES food_items (id)
        )
    ''')
    
    # Create sample user
    password_hash = hashlib.sha256("demo123".encode()).hexdigest()
    cursor.execute('''
        INSERT OR REPLACE INTO users 
        (id, email, username, hashed_password, first_name, last_name, date_of_birth, 
         gender, height_cm, activity_level, goal_weight_kg, goal_type)
        VALUES (1, '<EMAIL>', 'demo', ?, 'Demo', 'User', '1990-01-01', 
                'male', 175, 'moderately_active', 75, 'lose_weight')
    ''', (password_hash,))
    
    # Create sample food items
    foods = [
        ('Chicken Breast', 'Generic', None, 165, 31, 0, 3.6, 0, 0, 74, 100, '100g'),
        ('Brown Rice', 'Generic', None, 111, 2.6, 23, 0.9, 1.8, 0.4, 5, 100, '100g cooked'),
        ('Broccoli', 'Generic', None, 34, 2.8, 7, 0.4, 2.6, 1.5, 33, 100, '100g'),
        ('Banana', 'Generic', None, 89, 1.1, 23, 0.3, 2.6, 12, 1, 100, '1 medium'),
        ('Oatmeal', 'Generic', None, 68, 2.4, 12, 1.4, 1.7, 0.3, 49, 100, '100g cooked'),
        ('Salmon', 'Generic', None, 208, 20, 0, 12, 0, 0, 59, 100, '100g'),
        ('Greek Yogurt', 'Generic', None, 59, 10, 3.6, 0.4, 0, 3.6, 36, 100, '100g'),
        ('Almonds', 'Generic', None, 579, 21, 22, 50, 12, 4.4, 1, 28, '28g (1 oz)'),
    ]
    
    for food in foods:
        cursor.execute('''
            INSERT OR REPLACE INTO food_items 
            (name, brand, barcode, calories_per_100g, protein_per_100g, carbs_per_100g, 
             fat_per_100g, fiber_per_100g, sugar_per_100g, sodium_per_100g, 
             serving_size_g, serving_description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', food)
    
    # Create sample biometric entries (last 30 days)
    base_weight = 80.0
    for i in range(30):
        date = datetime.now() - timedelta(days=29-i)
        # Simulate gradual weight loss
        weight = base_weight - (i * 0.1) + (i % 3 * 0.05)  # Some variation
        body_fat = 18.0 - (i * 0.05)
        water = 60.0 + (i % 5 * 0.2)
        muscle = 35.0 + (i * 0.02)
        bmi = weight / (1.75 ** 2)
        bmr = 10 * weight + 6.25 * 175 - 5 * 33 + 5  # Mifflin-St Jeor for male
        
        cursor.execute('''
            INSERT INTO biometric_entries 
            (user_id, recorded_at, weight_kg, body_fat_percentage, water_percentage, 
             muscle_mass_kg, visceral_fat_rating, bmi, bmr_calculated)
            VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (date.isoformat(), weight, body_fat, water, muscle, 8.0, bmi, bmr))
    
    # Create sample nutrition entries
    for i in range(15):  # Last 15 days
        date = datetime.now() - timedelta(days=14-i)
        
        # Breakfast
        cursor.execute('''
            INSERT INTO nutrition_entries 
            (user_id, food_item_id, consumed_at, quantity, unit, calories, protein, carbs, fat)
            VALUES (1, 5, ?, 100, 'g', 68, 2.4, 12, 1.4)
        ''', ((date + timedelta(hours=8)).isoformat(),))
        
        # Lunch
        cursor.execute('''
            INSERT INTO nutrition_entries 
            (user_id, food_item_id, consumed_at, quantity, unit, calories, protein, carbs, fat)
            VALUES (1, 1, ?, 150, 'g', 248, 46.5, 0, 5.4)
        ''', ((date + timedelta(hours=12)).isoformat(),))
        
        # Dinner
        cursor.execute('''
            INSERT INTO nutrition_entries 
            (user_id, food_item_id, consumed_at, quantity, unit, calories, protein, carbs, fat)
            VALUES (1, 6, ?, 120, 'g', 250, 24, 0, 14.4)
        ''', ((date + timedelta(hours=19)).isoformat(),))
    
    conn.commit()
    conn.close()
    print("Sample data created successfully!")

if __name__ == "__main__":
    create_sample_data()
