"""
Analytics and data visualization router
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc

from app.database import get_db
from app.models import User, BiometricEntry, NutritionEntry, ExerciseEntry
from app.schemas import WeightTrend, DailyNutritionSummary
from app.auth import get_current_active_user

router = APIRouter()

@router.get("/weight-trend", response_model=List[WeightTrend])
async def get_weight_trend(
    days: int = Query(30, description="Number of days to include in trend"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get weight trend data for charts"""
    start_date = date.today() - timedelta(days=days)
    
    entries = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.user_id == current_user.id,
            BiometricEntry.weight_lbs.isnot(None),
            func.date(BiometricEntry.recorded_at) >= start_date
        )
    ).order_by(BiometricEntry.recorded_at).all()

    return [
        WeightTrend(
            date=entry.recorded_at,
            weight_lbs=entry.weight_lbs,
            bmi=entry.bmi
        )
        for entry in entries
    ]

@router.get("/biometric-trends")
async def get_biometric_trends(
    metrics: List[str] = Query(["weight_kg", "body_fat_percentage"], description="Metrics to include"),
    days: int = Query(30, description="Number of days to include"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get multiple biometric trends for overlay charts"""
    start_date = date.today() - timedelta(days=days)
    
    entries = db.query(BiometricEntry).filter(
        and_(
            BiometricEntry.user_id == current_user.id,
            func.date(BiometricEntry.recorded_at) >= start_date
        )
    ).order_by(BiometricEntry.recorded_at).all()
    
    trends = {}
    for metric in metrics:
        trends[metric] = []
        for entry in entries:
            value = getattr(entry, metric, None)
            if value is not None:
                trends[metric].append({
                    "date": entry.recorded_at.isoformat(),
                    "value": value
                })
    
    return trends

@router.get("/nutrition-trends")
async def get_nutrition_trends(
    days: int = Query(30, description="Number of days to include"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get daily nutrition trends"""
    start_date = date.today() - timedelta(days=days)
    
    # Get daily nutrition summaries
    daily_summaries = []
    current_date = start_date
    
    while current_date <= date.today():
        entries = db.query(NutritionEntry).filter(
            and_(
                NutritionEntry.user_id == current_user.id,
                func.date(NutritionEntry.consumed_at) == current_date
            )
        ).all()
        
        total_calories = sum(entry.calories or 0 for entry in entries)
        total_protein = sum(entry.protein or 0 for entry in entries)
        total_carbs = sum(entry.carbs or 0 for entry in entries)
        total_fat = sum(entry.fat or 0 for entry in entries)
        total_water = sum(entry.water_ml or 0 for entry in entries)
        
        daily_summaries.append({
            "date": current_date.isoformat(),
            "calories": total_calories,
            "protein": total_protein,
            "carbs": total_carbs,
            "fat": total_fat,
            "water_ml": total_water
        })
        
        current_date += timedelta(days=1)
    
    return daily_summaries

@router.get("/macro-breakdown")
async def get_macro_breakdown(
    target_date: Optional[date] = Query(default_factory=date.today),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get macro breakdown for pie chart"""
    entries = db.query(NutritionEntry).filter(
        and_(
            NutritionEntry.user_id == current_user.id,
            func.date(NutritionEntry.consumed_at) == target_date
        )
    ).all()
    
    total_protein = sum(entry.protein or 0 for entry in entries)
    total_carbs = sum(entry.carbs or 0 for entry in entries)
    total_fat = sum(entry.fat or 0 for entry in entries)
    
    # Calculate calories from macros (protein: 4 cal/g, carbs: 4 cal/g, fat: 9 cal/g)
    protein_calories = total_protein * 4
    carb_calories = total_carbs * 4
    fat_calories = total_fat * 9
    
    total_macro_calories = protein_calories + carb_calories + fat_calories
    
    if total_macro_calories == 0:
        return {
            "protein_percentage": 0,
            "carbs_percentage": 0,
            "fat_percentage": 0,
            "total_calories": 0
        }
    
    return {
        "protein_percentage": round((protein_calories / total_macro_calories) * 100, 1),
        "carbs_percentage": round((carb_calories / total_macro_calories) * 100, 1),
        "fat_percentage": round((fat_calories / total_macro_calories) * 100, 1),
        "total_calories": total_macro_calories,
        "protein_grams": total_protein,
        "carbs_grams": total_carbs,
        "fat_grams": total_fat
    }

@router.get("/exercise-summary")
async def get_exercise_summary(
    days: int = Query(7, description="Number of days to include"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get exercise summary for the specified period"""
    start_date = date.today() - timedelta(days=days)
    
    entries = db.query(ExerciseEntry).filter(
        and_(
            ExerciseEntry.user_id == current_user.id,
            func.date(ExerciseEntry.performed_at) >= start_date
        )
    ).all()
    
    total_calories = sum(entry.calories_burned or 0 for entry in entries)
    total_duration = sum(entry.duration_minutes or 0 for entry in entries)
    total_steps = sum(entry.steps or 0 for entry in entries)
    total_distance = sum(entry.distance_miles or 0 for entry in entries)
    
    # Group by exercise type
    exercise_breakdown = {}
    for entry in entries:
        if entry.exercise_type:
            type_name = entry.exercise_type.name
            if type_name not in exercise_breakdown:
                exercise_breakdown[type_name] = {
                    "count": 0,
                    "total_duration": 0,
                    "total_calories": 0
                }
            exercise_breakdown[type_name]["count"] += 1
            exercise_breakdown[type_name]["total_duration"] += entry.duration_minutes or 0
            exercise_breakdown[type_name]["total_calories"] += entry.calories_burned or 0
    
    return {
        "period_days": days,
        "total_calories_burned": total_calories,
        "total_duration_minutes": total_duration,
        "total_steps": total_steps,
        "total_distance_miles": total_distance,
        "exercise_sessions": len(entries),
        "exercise_breakdown": exercise_breakdown,
        "average_daily_calories": total_calories / days if days > 0 else 0,
        "average_daily_duration": total_duration / days if days > 0 else 0
    }

@router.get("/progress-summary")
async def get_progress_summary(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get overall progress summary"""
    # Get latest biometric entry
    latest_biometric = db.query(BiometricEntry).filter(
        BiometricEntry.user_id == current_user.id
    ).order_by(desc(BiometricEntry.recorded_at)).first()
    
    # Get first biometric entry for comparison
    first_biometric = db.query(BiometricEntry).filter(
        BiometricEntry.user_id == current_user.id
    ).order_by(BiometricEntry.recorded_at).first()
    
    # Calculate weight change
    weight_change = None
    if latest_biometric and first_biometric and latest_biometric.weight_lbs and first_biometric.weight_lbs:
        weight_change = latest_biometric.weight_lbs - first_biometric.weight_lbs
    
    # Get recent nutrition averages (last 7 days)
    week_ago = date.today() - timedelta(days=7)
    recent_nutrition = db.query(NutritionEntry).filter(
        and_(
            NutritionEntry.user_id == current_user.id,
            func.date(NutritionEntry.consumed_at) >= week_ago
        )
    ).all()
    
    avg_daily_calories = 0
    if recent_nutrition:
        total_calories = sum(entry.calories or 0 for entry in recent_nutrition)
        days_with_data = len(set(entry.consumed_at.date() for entry in recent_nutrition))
        avg_daily_calories = total_calories / max(days_with_data, 1)
    
    # Get recent exercise data
    recent_exercise = db.query(ExerciseEntry).filter(
        and_(
            ExerciseEntry.user_id == current_user.id,
            func.date(ExerciseEntry.performed_at) >= week_ago
        )
    ).all()
    
    weekly_exercise_calories = sum(entry.calories_burned or 0 for entry in recent_exercise)
    
    return {
        "current_weight_lbs": latest_biometric.weight_lbs if latest_biometric else None,
        "weight_change_lbs": weight_change,
        "current_bmi": latest_biometric.bmi if latest_biometric else None,
        "goal_weight_lbs": current_user.goal_weight_lbs,
        "avg_daily_calories_7d": round(avg_daily_calories, 1),
        "total_exercise_calories_7d": weekly_exercise_calories,
        "tracking_days": (latest_biometric.recorded_at.date() - first_biometric.recorded_at.date()).days + 1 if latest_biometric and first_biometric else 0
    }

@router.get("/calorie-balance")
async def get_calorie_balance(
    days: int = Query(7, description="Number of days to analyze"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get calorie balance analysis (intake vs expenditure)"""
    start_date = date.today() - timedelta(days=days)
    
    daily_balance = []
    current_date = start_date
    
    while current_date <= date.today():
        # Get nutrition for the day
        nutrition_entries = db.query(NutritionEntry).filter(
            and_(
                NutritionEntry.user_id == current_user.id,
                func.date(NutritionEntry.consumed_at) == current_date
            )
        ).all()
        
        calories_consumed = sum(entry.calories or 0 for entry in nutrition_entries)
        
        # Get exercise for the day
        exercise_entries = db.query(ExerciseEntry).filter(
            and_(
                ExerciseEntry.user_id == current_user.id,
                func.date(ExerciseEntry.performed_at) == current_date
            )
        ).all()
        
        calories_burned = sum(entry.calories_burned or 0 for entry in exercise_entries)
        
        # Estimate BMR (you might want to use the calculated BMR from biometric entries)
        estimated_bmr = 1800  # Default, should be calculated based on user data
        
        total_expenditure = estimated_bmr + calories_burned
        net_calories = calories_consumed - total_expenditure
        
        daily_balance.append({
            "date": current_date.isoformat(),
            "calories_consumed": calories_consumed,
            "calories_burned_exercise": calories_burned,
            "estimated_bmr": estimated_bmr,
            "total_expenditure": total_expenditure,
            "net_calories": net_calories
        })
        
        current_date += timedelta(days=1)
    
    return daily_balance
