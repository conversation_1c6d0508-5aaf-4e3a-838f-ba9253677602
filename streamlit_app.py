"""
Weight Loss Tracker - Streamlit Frontend Application
"""
import streamlit as st
import requests
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, date, timedelta
import json

def format_height(height_inches):
    """Format height in feet and inches"""
    if height_inches:
        feet = int(height_inches // 12)
        inches = height_inches % 12
        return f"{feet}'{inches:.1f}\""
    return "Not set"

def format_weight(weight_lbs):
    """Format weight in pounds"""
    if weight_lbs:
        return f"{weight_lbs:.1f} lbs"
    return "Not set"

# Configuration
API_BASE_URL = "http://localhost:8000/api"

# Page configuration
st.set_page_config(
    page_title="Weight Loss Tracker",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-message {
        color: #28a745;
        font-weight: bold;
    }
    .error-message {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

# Session state initialization
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'access_token' not in st.session_state:
    st.session_state.access_token = None
if 'user_data' not in st.session_state:
    st.session_state.user_data = None

def make_authenticated_request(method, endpoint, data=None, params=None):
    """Make an authenticated API request"""
    headers = {}
    if st.session_state.access_token:
        headers["Authorization"] = f"Bearer {st.session_state.access_token}"
    
    url = f"{API_BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        
        if response.status_code == 401:
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.error("Session expired. Please log in again.")
            return None
        
        return response
    except requests.exceptions.ConnectionError:
        st.error("Cannot connect to the API server. Please ensure the backend is running.")
        return None

def login_page():
    """Login page"""
    st.markdown('<h1 class="main-header">⚖️ Weight Loss Tracker</h1>', unsafe_allow_html=True)
    
    tab1, tab2 = st.tabs(["Login", "Register"])
    
    with tab1:
        st.subheader("Login")

        # Quick demo login button
        if st.button("🚀 Use Demo Account", type="primary"):
            try:
                response = requests.post(
                    f"{API_BASE_URL}/auth/login",
                    data={"username": "demo", "password": "demo123"}
                )

                if response.status_code == 200:
                    token_data = response.json()
                    st.session_state.access_token = token_data["access_token"]
                    st.session_state.authenticated = True
                    st.success("Demo login successful!")
                    st.rerun()
                else:
                    st.error("Demo account not available")
            except requests.exceptions.ConnectionError:
                st.error("Cannot connect to the API server. Please ensure the backend is running.")

        st.divider()

        with st.form("login_form"):
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            submit = st.form_submit_button("Login")

            if submit:
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/auth/login",
                        data={"username": username, "password": password}
                    )

                    if response.status_code == 200:
                        token_data = response.json()
                        st.session_state.access_token = token_data["access_token"]
                        st.session_state.authenticated = True
                        st.success("Login successful!")
                        st.rerun()
                    else:
                        st.error("Invalid credentials")
                except requests.exceptions.ConnectionError:
                    st.error("Cannot connect to the API server. Please ensure the backend is running.")
    
    with tab2:
        st.subheader("Quick Setup")
        st.info("💡 For local use - just set a username, password, and basic info to get started! All measurements use US units (lbs, inches, miles).")
        with st.form("register_form"):
            username = st.text_input("Username", value="user1")
            password = st.text_input("Password", type="password", value="password123")

            col1, col2 = st.columns(2)
            with col1:
                height_feet = st.number_input("Height (feet)", min_value=3, max_value=8, value=5)
                height_inches = st.number_input("Height (inches)", min_value=0.0, max_value=11.9, value=8.0, step=0.1)
                total_height_inches = height_feet * 12 + height_inches
                birth_date = st.date_input("Date of Birth", value=date(1990, 1, 1), max_value=date.today())
            with col2:
                goal_weight_lbs = st.number_input("Goal Weight (lbs)", min_value=70.0, max_value=500.0, value=150.0)
                activity_level = st.selectbox("Activity Level", [
                    "lightly_active", "moderately_active", "very_active", "sedentary", "extra_active"
                ])
                goal_type = st.selectbox("Goal Type", ["lose_weight", "maintain_weight", "gain_weight"])

            submit = st.form_submit_button("Create Account")

            if submit:
                # Generate a simple email for local use
                email = f"{username}@local.app"
                user_data = {
                    "email": email,
                    "username": username,
                    "password": password,
                    "first_name": "User",
                    "last_name": "Local",
                    "date_of_birth": birth_date.isoformat(),
                    "gender": "other",
                    "height_inches": total_height_inches,
                    "activity_level": activity_level,
                    "goal_weight_lbs": goal_weight_lbs,
                    "goal_type": goal_type
                }
                
                try:
                    response = requests.post(f"{API_BASE_URL}/auth/register", json=user_data)
                    
                    if response.status_code == 200:
                        st.success("Registration successful! Please log in.")
                    else:
                        error_detail = response.json().get("detail", "Registration failed")
                        st.error(f"Registration failed: {error_detail}")
                except requests.exceptions.ConnectionError:
                    st.error("Cannot connect to the API server. Please ensure the backend is running.")

def dashboard_page():
    """Main dashboard page"""
    st.markdown('<h1 class="main-header">📊 Dashboard</h1>', unsafe_allow_html=True)
    
    # Get user data
    if not st.session_state.user_data:
        response = make_authenticated_request("GET", "/auth/me")
        if response and response.status_code == 200:
            st.session_state.user_data = response.json()
    
    # Get progress summary
    response = make_authenticated_request("GET", "/analytics/progress-summary")
    if response and response.status_code == 200:
        progress_data = response.json()
        
        # Display key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            current_weight = progress_data.get("current_weight_lbs")
            if current_weight:
                st.metric("Current Weight", f"{current_weight:.1f} lbs")
            else:
                st.metric("Current Weight", "No data")

        with col2:
            weight_change = progress_data.get("weight_change_lbs")
            if weight_change is not None:
                st.metric("Weight Change", f"{weight_change:+.1f} lbs", delta=f"{weight_change:+.1f}")
            else:
                st.metric("Weight Change", "No data")

        with col3:
            current_bmi = progress_data.get("current_bmi")
            if current_bmi:
                st.metric("Current BMI", f"{current_bmi:.1f}")
            else:
                st.metric("Current BMI", "No data")

        with col4:
            goal_weight = progress_data.get("goal_weight_lbs")
            if goal_weight:
                st.metric("Goal Weight", f"{goal_weight:.1f} lbs")
            else:
                st.metric("Goal Weight", "Not set")
    
    # Charts section
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Weight Trend")
        response = make_authenticated_request("GET", "/analytics/weight-trend", params={"days": 30})
        if response and response.status_code == 200:
            weight_data = response.json()
            if weight_data:
                df = pd.DataFrame(weight_data)
                df['date'] = pd.to_datetime(df['date'], format='ISO8601')
                
                fig = px.line(df, x='date', y='weight_lbs', title='Weight Trend (30 days)')
                fig.update_layout(xaxis_title="Date", yaxis_title="Weight (lbs)")
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No weight data available")
    
    with col2:
        st.subheader("Today's Macro Breakdown")
        response = make_authenticated_request("GET", "/analytics/macro-breakdown")
        if response and response.status_code == 200:
            macro_data = response.json()
            if macro_data["total_calories"] > 0:
                labels = ['Protein', 'Carbs', 'Fat']
                values = [
                    macro_data["protein_percentage"],
                    macro_data["carbs_percentage"],
                    macro_data["fat_percentage"]
                ]
                
                fig = px.pie(values=values, names=labels, title='Macro Distribution')
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No nutrition data for today")

def biometrics_page():
    """Biometrics tracking page"""
    st.markdown('<h1 class="main-header">📏 Biometrics</h1>', unsafe_allow_html=True)
    
    tab1, tab2 = st.tabs(["Add Entry", "View History"])
    
    with tab1:
        st.subheader("Add Biometric Entry")
        st.info("📏 Enter your measurements in US units (pounds, inches)")
        with st.form("biometric_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                weight_lbs = st.number_input("Weight (lbs)", min_value=70.0, max_value=500.0, step=0.1)
                body_fat_percentage = st.number_input("Body Fat %", min_value=0.0, max_value=50.0, step=0.1)
                water_percentage = st.number_input("Water %", min_value=0.0, max_value=100.0, step=0.1)
                bone_mass_lbs = st.number_input("Bone Mass (lbs)", min_value=0.0, max_value=20.0, step=0.1)

            with col2:
                muscle_mass_lbs = st.number_input("Muscle Mass (lbs)", min_value=0.0, max_value=200.0, step=0.1)
                visceral_fat_rating = st.number_input("Visceral Fat Rating", min_value=0.0, max_value=30.0, step=0.1)
                notes = st.text_area("Notes")
            
            submit = st.form_submit_button("Add Entry")
            
            if submit:
                entry_data = {
                    "weight_lbs": weight_lbs if weight_lbs > 0 else None,
                    "body_fat_percentage": body_fat_percentage if body_fat_percentage > 0 else None,
                    "water_percentage": water_percentage if water_percentage > 0 else None,
                    "bone_mass_lbs": bone_mass_lbs if bone_mass_lbs > 0 else None,
                    "muscle_mass_lbs": muscle_mass_lbs if muscle_mass_lbs > 0 else None,
                    "visceral_fat_rating": visceral_fat_rating if visceral_fat_rating > 0 else None,
                    "notes": notes if notes else None
                }
                
                response = make_authenticated_request("POST", "/biometrics/", data=entry_data)
                if response and response.status_code == 200:
                    st.success("Biometric entry added successfully!")
                    st.rerun()
                else:
                    st.error("Failed to add biometric entry")
    
    with tab2:
        st.subheader("Biometric History")
        response = make_authenticated_request("GET", "/biometrics/", params={"limit": 50})
        if response and response.status_code == 200:
            entries = response.json()
            if entries:
                df = pd.DataFrame(entries)
                df['recorded_at'] = pd.to_datetime(df['recorded_at']).dt.strftime('%Y-%m-%d %H:%M')
                
                # Display table
                st.dataframe(df[['recorded_at', 'weight_lbs', 'body_fat_percentage', 'bmi', 'bmr_calculated']], use_container_width=True)
                
                # Biometric trends chart
                st.subheader("Biometric Trends")
                metrics = st.multiselect(
                    "Select metrics to display",
                    ['weight_lbs', 'body_fat_percentage', 'muscle_mass_lbs', 'water_percentage'],
                    default=['weight_lbs', 'body_fat_percentage']
                )
                
                if metrics:
                    response = make_authenticated_request("GET", "/analytics/biometric-trends", 
                                                        params={"metrics": metrics, "days": 60})
                    if response and response.status_code == 200:
                        trends_data = response.json()
                        
                        fig = make_subplots(specs=[[{"secondary_y": True}]])
                        
                        for i, metric in enumerate(metrics):
                            if metric in trends_data and trends_data[metric]:
                                df_metric = pd.DataFrame(trends_data[metric])
                                df_metric['date'] = pd.to_datetime(df_metric['date'])
                                
                                fig.add_trace(
                                    go.Scatter(x=df_metric['date'], y=df_metric['value'], 
                                             name=metric.replace('_', ' ').title()),
                                    secondary_y=(i > 0)
                                )
                        
                        fig.update_layout(title="Biometric Trends")
                        st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No biometric entries found")

def main():
    """Main application"""
    if not st.session_state.authenticated:
        login_page()
    else:
        # Sidebar navigation
        st.sidebar.title("Navigation")
        page = st.sidebar.selectbox("Choose a page", [
            "Dashboard", "Biometrics", "Nutrition", "Meals", "Exercise", "Analytics"
        ])
        
        if st.sidebar.button("Logout"):
            st.session_state.authenticated = False
            st.session_state.access_token = None
            st.session_state.user_data = None
            st.rerun()
        
        # Page routing
        if page == "Dashboard":
            dashboard_page()
        elif page == "Biometrics":
            biometrics_page()
        # Add other pages here...
        else:
            st.info(f"{page} page coming soon!")

if __name__ == "__main__":
    main()
