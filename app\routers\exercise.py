"""
Exercise and activity tracking router
"""
from typing import List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func

from app.database import get_db
from app.models import User, ExerciseType, ExerciseEntry
from app.schemas import (
    ExerciseTypeBase, ExerciseType as ExerciseTypeSchema,
    ExerciseEntryCreate, ExerciseEntry as ExerciseEntrySchema
)
from app.auth import get_current_active_user
from app.utils import lbs_to_kg

router = APIRouter()

def calculate_calories_burned(user: User, exercise_type: ExerciseType, duration_minutes: float, intensity: str = "moderate", weight_lbs: float = None) -> float:
    """
    Calculate calories burned using MET values
    Formula: Calories = MET × weight(kg) × time(hours)
    """
    if not user or not exercise_type or not exercise_type.met_value:
        return 0

    # Use provided weight or default
    if weight_lbs is None:
        weight_lbs = 154  # Default weight in lbs, should be replaced with actual user weight

    # Convert to kg for MET calculation
    weight_kg = lbs_to_kg(weight_lbs)

    # Adjust MET value based on intensity
    intensity_multipliers = {
        "low": 0.8,
        "moderate": 1.0,
        "high": 1.3,
        "very_high": 1.6
    }

    adjusted_met = exercise_type.met_value * intensity_multipliers.get(intensity, 1.0)
    hours = duration_minutes / 60.0

    return adjusted_met * weight_kg * hours

# Exercise Types endpoints
@router.post("/types", response_model=ExerciseTypeSchema)
async def create_exercise_type(
    exercise_type: ExerciseTypeBase,
    db: Session = Depends(get_db)
):
    """Create a new exercise type"""
    db_exercise_type = ExerciseType(**exercise_type.dict())
    db.add(db_exercise_type)
    db.commit()
    db.refresh(db_exercise_type)
    return db_exercise_type

@router.get("/types", response_model=List[ExerciseTypeSchema])
async def get_exercise_types(
    category: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get exercise types"""
    query = db.query(ExerciseType)
    
    if category:
        query = query.filter(ExerciseType.category == category)
    
    exercise_types = query.offset(skip).limit(limit).all()
    return exercise_types

@router.get("/types/{type_id}", response_model=ExerciseTypeSchema)
async def get_exercise_type(
    type_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific exercise type"""
    exercise_type = db.query(ExerciseType).filter(ExerciseType.id == type_id).first()
    if not exercise_type:
        raise HTTPException(status_code=404, detail="Exercise type not found")
    return exercise_type

# Exercise Entries endpoints
@router.post("/entries", response_model=ExerciseEntrySchema)
async def create_exercise_entry(
    entry: ExerciseEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new exercise entry"""
    # Calculate calories if not provided and we have exercise type + duration
    calories_burned = entry.calories_burned
    if not calories_burned and entry.exercise_type_id and entry.duration_minutes:
        exercise_type = db.query(ExerciseType).filter(ExerciseType.id == entry.exercise_type_id).first()
        if exercise_type:
            calories_burned = calculate_calories_burned(
                current_user, exercise_type, entry.duration_minutes, entry.intensity or "moderate"
            )
    
    db_entry = ExerciseEntry(
        user_id=current_user.id,
        exercise_type_id=entry.exercise_type_id,
        duration_minutes=entry.duration_minutes,
        intensity=entry.intensity,
        calories_burned=calories_burned,
        steps=entry.steps,
        distance_miles=entry.distance_miles,
        heart_rate_avg=entry.heart_rate_avg,
        heart_rate_max=entry.heart_rate_max,
        sets=entry.sets,
        reps=entry.reps,
        weight_lbs=entry.weight_lbs,
        notes=entry.notes
    )
    
    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.get("/entries", response_model=List[ExerciseEntrySchema])
async def get_exercise_entries(
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    exercise_type_id: Optional[int] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get exercise entries for the current user"""
    query = db.query(ExerciseEntry).filter(ExerciseEntry.user_id == current_user.id)
    
    if start_date:
        query = query.filter(func.date(ExerciseEntry.performed_at) >= start_date)
    if end_date:
        query = query.filter(func.date(ExerciseEntry.performed_at) <= end_date)
    if exercise_type_id:
        query = query.filter(ExerciseEntry.exercise_type_id == exercise_type_id)
    
    entries = query.order_by(desc(ExerciseEntry.performed_at)).offset(skip).limit(limit).all()
    return entries

@router.get("/entries/{entry_id}", response_model=ExerciseEntrySchema)
async def get_exercise_entry(
    entry_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific exercise entry"""
    entry = db.query(ExerciseEntry).filter(
        and_(
            ExerciseEntry.id == entry_id,
            ExerciseEntry.user_id == current_user.id
        )
    ).first()
    
    if not entry:
        raise HTTPException(status_code=404, detail="Exercise entry not found")
    
    return entry

@router.put("/entries/{entry_id}", response_model=ExerciseEntrySchema)
async def update_exercise_entry(
    entry_id: int,
    entry_update: ExerciseEntryCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update an exercise entry"""
    db_entry = db.query(ExerciseEntry).filter(
        and_(
            ExerciseEntry.id == entry_id,
            ExerciseEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Exercise entry not found")
    
    # Update fields
    for field, value in entry_update.dict(exclude_unset=True).items():
        setattr(db_entry, field, value)
    
    # Recalculate calories if needed
    if (entry_update.exercise_type_id or entry_update.duration_minutes or entry_update.intensity) and not entry_update.calories_burned:
        exercise_type = db.query(ExerciseType).filter(ExerciseType.id == db_entry.exercise_type_id).first()
        if exercise_type and db_entry.duration_minutes:
            db_entry.calories_burned = calculate_calories_burned(
                current_user, exercise_type, db_entry.duration_minutes, db_entry.intensity or "moderate"
            )
    
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.delete("/entries/{entry_id}")
async def delete_exercise_entry(
    entry_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete an exercise entry"""
    db_entry = db.query(ExerciseEntry).filter(
        and_(
            ExerciseEntry.id == entry_id,
            ExerciseEntry.user_id == current_user.id
        )
    ).first()
    
    if not db_entry:
        raise HTTPException(status_code=404, detail="Exercise entry not found")
    
    db.delete(db_entry)
    db.commit()
    
    return {"message": "Exercise entry deleted successfully"}

@router.get("/daily-summary")
async def get_daily_exercise_summary(
    target_date: Optional[date] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get daily exercise summary"""
    if not target_date:
        target_date = date.today()
    
    entries = db.query(ExerciseEntry).filter(
        and_(
            ExerciseEntry.user_id == current_user.id,
            func.date(ExerciseEntry.performed_at) == target_date
        )
    ).all()
    
    total_calories = sum(entry.calories_burned or 0 for entry in entries)
    total_steps = sum(entry.steps or 0 for entry in entries)
    total_distance = sum(entry.distance_km or 0 for entry in entries)
    total_duration = sum(entry.duration_minutes or 0 for entry in entries)
    
    return {
        "date": target_date,
        "total_calories_burned": total_calories,
        "total_steps": total_steps,
        "total_distance_km": total_distance,
        "total_duration_minutes": total_duration,
        "exercise_count": len(entries)
    }

# Fitness tracker integration endpoints
@router.post("/sync-fitness-data")
async def sync_fitness_tracker_data(
    steps: Optional[int] = None,
    calories_burned: Optional[float] = None,
    distance_km: Optional[float] = None,
    heart_rate_avg: Optional[int] = None,
    sync_date: Optional[date] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Sync data from fitness tracker"""
    if not sync_date:
        sync_date = date.today()
    
    # Create or update fitness tracker entry for the day
    existing_entry = db.query(ExerciseEntry).filter(
        and_(
            ExerciseEntry.user_id == current_user.id,
            func.date(ExerciseEntry.performed_at) == sync_date,
            ExerciseEntry.exercise_type_id.is_(None)  # Fitness tracker entries don't have specific exercise type
        )
    ).first()
    
    if existing_entry:
        # Update existing entry
        if steps is not None:
            existing_entry.steps = steps
        if calories_burned is not None:
            existing_entry.calories_burned = calories_burned
        if distance_km is not None:
            existing_entry.distance_km = distance_km
        if heart_rate_avg is not None:
            existing_entry.heart_rate_avg = heart_rate_avg
        
        db.commit()
        db.refresh(existing_entry)
        return existing_entry
    else:
        # Create new entry
        new_entry = ExerciseEntry(
            user_id=current_user.id,
            performed_at=datetime.combine(sync_date, datetime.min.time()),
            steps=steps,
            calories_burned=calories_burned,
            distance_km=distance_km,
            heart_rate_avg=heart_rate_avg,
            notes="Synced from fitness tracker"
        )
        
        db.add(new_entry)
        db.commit()
        db.refresh(new_entry)
        return new_entry
