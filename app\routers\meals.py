"""
Meals management router
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.database import get_db
from app.models import User, Meal, FoodItem, NutritionEntry, meal_ingredients
from app.schemas import MealC<PERSON>, Meal as MealSchema, NutritionEntryCreate
from app.auth import get_current_active_user

router = APIRouter()

@router.post("/", response_model=MealSchema)
async def create_meal(
    meal: MealCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new meal"""
    db_meal = Meal(
        user_id=current_user.id,
        name=meal.name,
        description=meal.description,
        meal_type=meal.meal_type,
        is_template=meal.is_template
    )
    
    db.add(db_meal)
    db.commit()
    db.refresh(db_meal)
    
    # Add food items to the meal
    if meal.food_item_ids:
        for food_id in meal.food_item_ids:
            food_item = db.query(FoodItem).filter(FoodItem.id == food_id).first()
            if food_item:
                db_meal.food_items.append(food_item)
        
        db.commit()
        db.refresh(db_meal)
    
    return db_meal

@router.get("/", response_model=List[MealSchema])
async def get_meals(
    skip: int = 0,
    limit: int = 100,
    templates_only: bool = False,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get meals for the current user"""
    query = db.query(Meal).filter(Meal.user_id == current_user.id)
    
    if templates_only:
        query = query.filter(Meal.is_template == True)
    
    meals = query.offset(skip).limit(limit).all()
    return meals

@router.get("/{meal_id}", response_model=MealSchema)
async def get_meal(
    meal_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific meal"""
    meal = db.query(Meal).filter(
        and_(
            Meal.id == meal_id,
            Meal.user_id == current_user.id
        )
    ).first()
    
    if not meal:
        raise HTTPException(status_code=404, detail="Meal not found")
    
    return meal

@router.put("/{meal_id}", response_model=MealSchema)
async def update_meal(
    meal_id: int,
    meal_update: MealCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a meal"""
    db_meal = db.query(Meal).filter(
        and_(
            Meal.id == meal_id,
            Meal.user_id == current_user.id
        )
    ).first()
    
    if not db_meal:
        raise HTTPException(status_code=404, detail="Meal not found")
    
    # Update basic fields
    for field, value in meal_update.dict(exclude={'food_item_ids'}).items():
        if value is not None:
            setattr(db_meal, field, value)
    
    # Update food items if provided
    if meal_update.food_item_ids is not None:
        # Clear existing food items
        db_meal.food_items.clear()
        
        # Add new food items
        for food_id in meal_update.food_item_ids:
            food_item = db.query(FoodItem).filter(FoodItem.id == food_id).first()
            if food_item:
                db_meal.food_items.append(food_item)
    
    db.commit()
    db.refresh(db_meal)
    
    return db_meal

@router.delete("/{meal_id}")
async def delete_meal(
    meal_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a meal"""
    db_meal = db.query(Meal).filter(
        and_(
            Meal.id == meal_id,
            Meal.user_id == current_user.id
        )
    ).first()
    
    if not db_meal:
        raise HTTPException(status_code=404, detail="Meal not found")
    
    db.delete(db_meal)
    db.commit()
    
    return {"message": "Meal deleted successfully"}

@router.post("/{meal_id}/log")
async def log_meal_consumption(
    meal_id: int,
    portion_multiplier: float = 1.0,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Log consumption of a saved meal"""
    # Get the meal
    meal = db.query(Meal).filter(
        and_(
            Meal.id == meal_id,
            Meal.user_id == current_user.id
        )
    ).first()
    
    if not meal:
        raise HTTPException(status_code=404, detail="Meal not found")
    
    # Create nutrition entries for each food item in the meal
    nutrition_entries = []
    
    # Get meal ingredients with quantities
    meal_ingredient_data = db.execute(
        meal_ingredients.select().where(meal_ingredients.c.meal_id == meal_id)
    ).fetchall()
    
    for ingredient in meal_ingredient_data:
        food_item = db.query(FoodItem).filter(FoodItem.id == ingredient.food_item_id).first()
        if food_item:
            # Calculate adjusted quantity based on portion multiplier
            adjusted_quantity = ingredient.quantity * portion_multiplier
            
            # Calculate nutritional values
            from app.routers.nutrition import calculate_nutrition_values
            nutrition_values = calculate_nutrition_values(food_item, adjusted_quantity, ingredient.unit)
            
            # Create nutrition entry
            nutrition_entry = NutritionEntry(
                user_id=current_user.id,
                food_item_id=food_item.id,
                meal_id=meal_id,
                quantity=adjusted_quantity,
                unit=ingredient.unit,
                **nutrition_values
            )
            
            db.add(nutrition_entry)
            nutrition_entries.append(nutrition_entry)
    
    db.commit()
    
    return {
        "message": f"Meal '{meal.name}' logged successfully",
        "entries_created": len(nutrition_entries),
        "portion_multiplier": portion_multiplier
    }

@router.post("/{meal_id}/add-ingredient")
async def add_ingredient_to_meal(
    meal_id: int,
    food_item_id: int,
    quantity: float = 1.0,
    unit: str = "serving",
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Add an ingredient to a meal with specific quantity"""
    # Verify meal ownership
    meal = db.query(Meal).filter(
        and_(
            Meal.id == meal_id,
            Meal.user_id == current_user.id
        )
    ).first()
    
    if not meal:
        raise HTTPException(status_code=404, detail="Meal not found")
    
    # Verify food item exists
    food_item = db.query(FoodItem).filter(FoodItem.id == food_item_id).first()
    if not food_item:
        raise HTTPException(status_code=404, detail="Food item not found")
    
    # Add or update ingredient in meal
    existing = db.execute(
        meal_ingredients.select().where(
            and_(
                meal_ingredients.c.meal_id == meal_id,
                meal_ingredients.c.food_item_id == food_item_id
            )
        )
    ).fetchone()
    
    if existing:
        # Update existing ingredient
        db.execute(
            meal_ingredients.update().where(
                and_(
                    meal_ingredients.c.meal_id == meal_id,
                    meal_ingredients.c.food_item_id == food_item_id
                )
            ).values(quantity=quantity, unit=unit)
        )
    else:
        # Add new ingredient
        db.execute(
            meal_ingredients.insert().values(
                meal_id=meal_id,
                food_item_id=food_item_id,
                quantity=quantity,
                unit=unit
            )
        )
    
    db.commit()
    
    return {"message": "Ingredient added to meal successfully"}

@router.delete("/{meal_id}/ingredients/{food_item_id}")
async def remove_ingredient_from_meal(
    meal_id: int,
    food_item_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Remove an ingredient from a meal"""
    # Verify meal ownership
    meal = db.query(Meal).filter(
        and_(
            Meal.id == meal_id,
            Meal.user_id == current_user.id
        )
    ).first()
    
    if not meal:
        raise HTTPException(status_code=404, detail="Meal not found")
    
    # Remove ingredient
    result = db.execute(
        meal_ingredients.delete().where(
            and_(
                meal_ingredients.c.meal_id == meal_id,
                meal_ingredients.c.food_item_id == food_item_id
            )
        )
    )
    
    if result.rowcount == 0:
        raise HTTPException(status_code=404, detail="Ingredient not found in meal")
    
    db.commit()
    
    return {"message": "Ingredient removed from meal successfully"}
