# Weight Loss Tracker 🏃‍♂️⚖️

A comprehensive health and fitness tracking application designed to support your weight loss journey. This application provides a one-stop solution for tracking biometrics, nutrition, exercise, and analyzing your progress with intelligent insights.

## Features

### 🔢 Comprehensive Biometric Tracking
- Weight tracking
- Body fat percentage
- Water percentage  
- Bone mass (kg)
- BMI calculation
- Muscle mass (kg)
- Visceral fat rating
- Automated BMR calculation

### 🍎 Advanced Nutrition Tracking
- Extensive food database
- Macro tracking (protein, carbs, fat)
- Calorie tracking
- Sodium and fiber tracking
- Water, coffee, and beverage tracking
- Custom food entry

### 🍽️ Meal Management System
- Create and save custom meals
- Meal templates for easy reuse
- Ingredient management with portions
- Quick meal logging
- Meal scaling and portion adjustment

### 🏋️ Exercise & Activity Tracking
- Workout logging
- Active calorie tracking
- Step count integration
- Fitness tracker data sync
- Exercise type categorization
- Heart rate monitoring

### 📊 Data Visualization & Analytics
- Interactive trendline charts
- Metric overlay capabilities (up to 2 metrics)
- Pie charts for macro breakdowns
- Progress summaries
- Calorie balance analysis
- Historical data views

### 🧠 Intelligent BMR Prediction Engine
- Machine learning-powered BMR estimation
- Adapts to individual metabolic patterns
- Considers exercise history and body composition
- Provides personalized insights
- Tracks metabolic adaptations over time

## Technology Stack

- **Backend**: FastAPI (Python)
- **Frontend**: Streamlit
- **Database**: SQLite (easily upgradeable to PostgreSQL)
- **Machine Learning**: Scikit-learn
- **Data Visualization**: Plotly
- **Authentication**: JWT tokens with bcrypt hashing

## Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation & Setup

1. **Clone or download the project**
   ```bash
   cd Weightloss_App
   ```

2. **Run the startup script**
   ```bash
   python start_app.py
   ```

   This script will:
   - Install all required dependencies
   - Set up the database with sample data
   - Start the API server (port 8000)
   - Launch the Streamlit web interface (port 8501)

3. **Access the application**
   - Open your browser to `http://localhost:8501`
   - Use the demo account:
     - Username: `demo`
     - Password: `demo123`

### Manual Setup (Alternative)

If the automatic setup doesn't work:

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up the database**
   ```bash
   python setup_sample_data.py
   ```

3. **Start the API server**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

4. **Start the Streamlit app** (in a new terminal)
   ```bash
   streamlit run streamlit_app.py --server.port 8501
   ```

## Usage Guide

### Getting Started
1. **Register** a new account or use the demo account
2. **Set up your profile** with basic information (height, activity level, goals)
3. **Add your first biometric entry** with current weight and body composition
4. **Log your meals** using the nutrition tracker
5. **Record workouts** and daily activities
6. **View your progress** on the dashboard and analytics pages

### Key Features

#### Biometric Tracking
- Navigate to the "Biometrics" page
- Add entries with weight, body fat %, and other measurements
- View historical trends and patterns
- BMI and BMR are calculated automatically

#### Nutrition Tracking
- Use the "Nutrition" page to log food intake
- Search the food database or add custom foods
- Track macros and calories automatically
- View daily nutrition summaries

#### Meal Management
- Create meal templates on the "Meals" page
- Save frequently eaten meals for quick logging
- Scale portions up or down as needed
- Log entire meals with one click

#### Exercise Tracking
- Record workouts on the "Exercise" page
- Log cardio, strength training, and activities
- Sync data from fitness trackers
- Track calories burned and performance metrics

#### Analytics & Insights
- View comprehensive charts on the "Analytics" page
- Overlay multiple metrics for correlation analysis
- Monitor macro distribution with pie charts
- Track calorie balance and progress trends

## API Documentation

Once the application is running, you can access the interactive API documentation at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Database Schema

The application uses a comprehensive relational database schema with the following main entities:
- **Users**: User profiles and settings
- **BiometricEntries**: Weight, body composition, and health metrics
- **FoodItems**: Nutrition database with macro information
- **NutritionEntries**: Food consumption logs
- **Meals**: Saved meal templates and compositions
- **ExerciseTypes**: Exercise categories and MET values
- **ExerciseEntries**: Workout and activity logs

## Machine Learning Features

The BMR prediction engine uses:
- **Random Forest Regression** for BMR prediction
- **Feature Engineering** including weight trends, exercise patterns, and body composition
- **Adaptive Learning** that improves predictions over time
- **Metabolic Adaptation Detection** to identify changes in metabolism

## Customization

### Adding New Food Items
- Use the nutrition tracking interface to add custom foods
- Include complete macro information for accurate tracking
- Foods are saved to the database for future use

### Exercise Types
- Add new exercise types through the API
- Include MET values for automatic calorie calculation
- Categorize exercises for better organization

### Data Export
- All data can be exported through the API endpoints
- Use the analytics endpoints for bulk data retrieval
- Integrate with external tools and services

## Troubleshooting

### Common Issues

1. **Cannot connect to API server**
   - Ensure the FastAPI server is running on port 8000
   - Check for port conflicts
   - Verify firewall settings

2. **Database errors**
   - Delete `weightloss_tracker.db` and run `setup_sample_data.py` again
   - Check file permissions in the project directory

3. **Package installation issues**
   - Update pip: `python -m pip install --upgrade pip`
   - Use virtual environment for isolation
   - Check Python version compatibility

### Getting Help

If you encounter issues:
1. Check the console output for error messages
2. Verify all dependencies are installed correctly
3. Ensure Python 3.8+ is being used
4. Try the manual setup process

## Future Enhancements

Planned features for future versions:
- Mobile app companion
- Integration with popular fitness trackers (Fitbit, Apple Health, etc.)
- Social features and community challenges
- Advanced meal planning and grocery lists
- Nutritionist and trainer collaboration tools
- Enhanced machine learning models
- Cloud synchronization and backup

## Contributing

This is a comprehensive weight loss tracking application designed to provide professional-grade health monitoring capabilities. The modular architecture makes it easy to extend with additional features and integrations.

## License

This project is provided as-is for personal use and learning purposes.
